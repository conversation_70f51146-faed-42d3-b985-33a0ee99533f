<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开区间与闭区间 - 零基础学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .title {
            text-align: center;
            color: white;
            font-size: 3rem;
            margin-bottom: 60px;
            opacity: 0;
            transform: translateY(-50px);
            animation: fadeInDown 1s ease-out forwards;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            opacity: 0;
            transform: translateY(50px);
            animation: fadeInUp 1s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .explanation {
            font-size: 1.2rem;
            line-height: 1.8;
            color: #555;
            margin-bottom: 30px;
            text-align: center;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            position: relative;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            background: white;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            font-size: 1.1rem;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #ffecd2, #fcb69f);
            color: #333;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .btn:active {
            transform: translateY(-1px);
        }

        .highlight {
            background: linear-gradient(45deg, #ff9a9e, #fecfef);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #ff6b6b;
        }

        .quiz-container {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 20px;
            padding: 30px;
            margin-top: 30px;
        }

        .quiz-question {
            font-size: 1.3rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .quiz-option {
            padding: 15px;
            background: white;
            border: 2px solid transparent;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-weight: bold;
        }

        .quiz-option:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .quiz-option.correct {
            background: #d4edda;
            border-color: #28a745;
        }

        .quiz-option.wrong {
            background: #f8d7da;
            border-color: #dc3545;
        }

        @keyframes fadeInDown {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .floating {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">📐 开区间与闭区间 📐</h1>
        
        <div class="section">
            <h2 class="section-title">🎯 什么是区间？</h2>
            <div class="explanation">
                区间就像是数轴上的一段路程，表示从一个数到另一个数之间的所有数字！<br>
                就像你从家走到学校，路上经过的所有地方都在这个"区间"里。
            </div>
            <div class="canvas-container">
                <canvas id="introCanvas" width="800" height="200"></canvas>
            </div>
            <div class="controls">
                <button class="btn btn-primary" onclick="animateIntro()">🎬 播放动画</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🔵 闭区间 [a, b]</h2>
            <div class="explanation">
                闭区间用方括号 [ ] 表示，包含端点的值！<br>
                就像一个封闭的盒子，连盒子的边界也算在里面。
            </div>
            <div class="highlight">
                <strong>记忆口诀：</strong> 方括号像门关上了，端点值也被"关"在里面！
            </div>
            <div class="canvas-container">
                <canvas id="closedCanvas" width="800" height="200"></canvas>
            </div>
            <div class="controls">
                <button class="btn btn-primary" onclick="animateClosedInterval()">🎬 演示闭区间</button>
                <button class="btn btn-secondary" onclick="showClosedExample()">📝 看例子</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">⭕ 开区间 (a, b)</h2>
            <div class="explanation">
                开区间用圆括号 ( ) 表示，不包含端点的值！<br>
                就像一个开口的盒子，边界上的东西会掉出去。
            </div>
            <div class="highlight">
                <strong>记忆口诀：</strong> 圆括号像门开着，端点值被"挡"在门外！
            </div>
            <div class="canvas-container">
                <canvas id="openCanvas" width="800" height="200"></canvas>
            </div>
            <div class="controls">
                <button class="btn btn-primary" onclick="animateOpenInterval()">🎬 演示开区间</button>
                <button class="btn btn-secondary" onclick="showOpenExample()">📝 看例子</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 对比学习</h2>
            <div class="explanation">
                让我们一起看看开区间和闭区间的区别！
            </div>
            <div class="canvas-container">
                <canvas id="compareCanvas" width="800" height="300"></canvas>
            </div>
            <div class="controls">
                <button class="btn btn-primary" onclick="animateComparison()">🔄 对比演示</button>
                <button class="btn btn-secondary" onclick="resetComparison()">🔄 重置</button>
            </div>
        </div>

        <div class="section">
            <div class="quiz-container">
                <h2 class="section-title">🧠 小测验</h2>
                <div class="quiz-question" id="quizQuestion">
                    区间 [2, 5) 包含数字 5 吗？
                </div>
                <div class="quiz-options" id="quizOptions">
                    <div class="quiz-option" onclick="checkAnswer(this, false)">包含，因为有括号</div>
                    <div class="quiz-option" onclick="checkAnswer(this, true)">不包含，因为是圆括号</div>
                    <div class="quiz-option" onclick="checkAnswer(this, false)">不确定</div>
                </div>
                <div id="quizResult" style="text-align: center; margin-top: 20px; font-size: 1.2rem; font-weight: bold;"></div>
                <div class="controls">
                    <button class="btn btn-primary" onclick="nextQuestion()" style="display: none;" id="nextBtn">下一题 ➡️</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let animationId;
        let currentQuestionIndex = 0;

        // 题目数据
        const questions = [
            {
                question: "区间 [2, 5) 包含数字 5 吗？",
                options: [
                    { text: "包含，因为有括号", correct: false },
                    { text: "不包含，因为是圆括号", correct: true },
                    { text: "不确定", correct: false }
                ],
                explanation: "右边是圆括号 )，表示不包含端点 5"
            },
            {
                question: "区间 (1, 3] 包含数字 1 吗？",
                options: [
                    { text: "包含", correct: false },
                    { text: "不包含", correct: true },
                    { text: "有时包含", correct: false }
                ],
                explanation: "左边是圆括号 (，表示不包含端点 1"
            },
            {
                question: "区间 [0, 10] 包含数字 0 和 10 吗？",
                options: [
                    { text: "都包含", correct: true },
                    { text: "都不包含", correct: false },
                    { text: "只包含一个", correct: false }
                ],
                explanation: "两边都是方括号 [ ]，表示两个端点都包含"
            }
        ];

        // 获取Canvas上下文
        function getCanvasContext(canvasId) {
            const canvas = document.getElementById(canvasId);
            return canvas.getContext('2d');
        }

        // 绘制数轴
        function drawNumberLine(ctx, canvas, start = -2, end = 8) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerY = canvas.height / 2;
            const leftX = 100;
            const rightX = canvas.width - 100;
            const lineLength = rightX - leftX;

            // 绘制主轴线
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(leftX - 20, centerY);
            ctx.lineTo(rightX + 20, centerY);
            ctx.stroke();

            // 绘制箭头
            ctx.beginPath();
            ctx.moveTo(rightX + 20, centerY);
            ctx.lineTo(rightX + 10, centerY - 8);
            ctx.moveTo(rightX + 20, centerY);
            ctx.lineTo(rightX + 10, centerY + 8);
            ctx.stroke();

            // 绘制刻度和数字
            const range = end - start;
            for (let i = 0; i <= 10; i++) {
                const x = leftX + (lineLength * i / 10);
                const value = start + (range * i / 10);

                ctx.beginPath();
                ctx.moveTo(x, centerY - 10);
                ctx.lineTo(x, centerY + 10);
                ctx.stroke();

                ctx.fillStyle = '#333';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(Math.round(value * 10) / 10, x, centerY + 30);
            }
        }

        // 介绍动画
        function animateIntro() {
            const canvas = document.getElementById('introCanvas');
            const ctx = getCanvasContext('introCanvas');

            drawNumberLine(ctx, canvas);

            let progress = 0;
            const animate = () => {
                drawNumberLine(ctx, canvas);

                // 绘制移动的小人
                const centerY = canvas.height / 2;
                const leftX = 100;
                const rightX = canvas.width - 100;
                const currentX = leftX + (rightX - leftX) * (progress / 100);

                // 小人
                ctx.fillStyle = '#ff6b6b';
                ctx.beginPath();
                ctx.arc(currentX, centerY - 40, 15, 0, 2 * Math.PI);
                ctx.fill();

                // 路径轨迹
                ctx.strokeStyle = '#ff6b6b';
                ctx.lineWidth = 5;
                ctx.setLineDash([5, 5]);
                ctx.beginPath();
                ctx.moveTo(leftX, centerY - 40);
                ctx.lineTo(currentX, centerY - 40);
                ctx.stroke();
                ctx.setLineDash([]);

                // 文字说明
                ctx.fillStyle = '#333';
                ctx.font = '18px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('小人在数轴上移动，经过的所有位置就是区间！', canvas.width / 2, 50);

                progress += 1;
                if (progress <= 100) {
                    animationId = requestAnimationFrame(animate);
                }
            };

            animate();
        }

        // 闭区间动画
        function animateClosedInterval() {
            const canvas = document.getElementById('closedCanvas');
            const ctx = getCanvasContext('closedCanvas');

            drawNumberLine(ctx, canvas, 0, 10);

            let progress = 0;
            const animate = () => {
                drawNumberLine(ctx, canvas, 0, 10);

                const centerY = canvas.height / 2;
                const leftX = 100;
                const rightX = canvas.width - 100;

                // 计算区间 [2, 6] 的位置
                const startPos = leftX + (rightX - leftX) * 0.2; // 2的位置
                const endPos = leftX + (rightX - leftX) * 0.6;   // 6的位置

                // 绘制区间高亮
                if (progress > 20) {
                    ctx.fillStyle = 'rgba(102, 126, 234, 0.3)';
                    ctx.fillRect(startPos, centerY - 20, endPos - startPos, 40);
                }

                // 绘制端点（实心圆表示包含）
                if (progress > 40) {
                    ctx.fillStyle = '#667eea';
                    ctx.beginPath();
                    ctx.arc(startPos, centerY, 8, 0, 2 * Math.PI);
                    ctx.fill();

                    ctx.beginPath();
                    ctx.arc(endPos, centerY, 8, 0, 2 * Math.PI);
                    ctx.fill();
                }

                // 绘制方括号
                if (progress > 60) {
                    ctx.strokeStyle = '#667eea';
                    ctx.lineWidth = 4;

                    // 左方括号
                    ctx.beginPath();
                    ctx.moveTo(startPos - 15, centerY - 25);
                    ctx.lineTo(startPos - 25, centerY - 25);
                    ctx.lineTo(startPos - 25, centerY + 25);
                    ctx.lineTo(startPos - 15, centerY + 25);
                    ctx.stroke();

                    // 右方括号
                    ctx.beginPath();
                    ctx.moveTo(endPos + 15, centerY - 25);
                    ctx.lineTo(endPos + 25, centerY - 25);
                    ctx.lineTo(endPos + 25, centerY + 25);
                    ctx.lineTo(endPos + 15, centerY + 25);
                    ctx.stroke();
                }

                // 文字说明
                ctx.fillStyle = '#333';
                ctx.font = '18px Microsoft YaHei';
                ctx.textAlign = 'center';

                if (progress > 80) {
                    ctx.fillText('闭区间 [2, 6] - 包含端点 2 和 6', canvas.width / 2, 50);
                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillText('实心圆 ● 表示包含这个点', canvas.width / 2, 180);
                }

                progress += 2;
                if (progress <= 100) {
                    animationId = requestAnimationFrame(animate);
                }
            };

            animate();
        }

        // 开区间动画
        function animateOpenInterval() {
            const canvas = document.getElementById('openCanvas');
            const ctx = getCanvasContext('openCanvas');

            drawNumberLine(ctx, canvas, 0, 10);

            let progress = 0;
            const animate = () => {
                drawNumberLine(ctx, canvas, 0, 10);

                const centerY = canvas.height / 2;
                const leftX = 100;
                const rightX = canvas.width - 100;

                // 计算区间 (3, 7) 的位置
                const startPos = leftX + (rightX - leftX) * 0.3; // 3的位置
                const endPos = leftX + (rightX - leftX) * 0.7;   // 7的位置

                // 绘制区间高亮
                if (progress > 20) {
                    ctx.fillStyle = 'rgba(255, 107, 107, 0.3)';
                    ctx.fillRect(startPos, centerY - 20, endPos - startPos, 40);
                }

                // 绘制端点（空心圆表示不包含）
                if (progress > 40) {
                    ctx.strokeStyle = '#ff6b6b';
                    ctx.lineWidth = 3;
                    ctx.fillStyle = 'white';

                    ctx.beginPath();
                    ctx.arc(startPos, centerY, 8, 0, 2 * Math.PI);
                    ctx.fill();
                    ctx.stroke();

                    ctx.beginPath();
                    ctx.arc(endPos, centerY, 8, 0, 2 * Math.PI);
                    ctx.fill();
                    ctx.stroke();
                }

                // 绘制圆括号
                if (progress > 60) {
                    ctx.strokeStyle = '#ff6b6b';
                    ctx.lineWidth = 4;

                    // 左圆括号
                    ctx.beginPath();
                    ctx.arc(startPos - 20, centerY, 20, -Math.PI/2, Math.PI/2);
                    ctx.stroke();

                    // 右圆括号
                    ctx.beginPath();
                    ctx.arc(endPos + 20, centerY, 20, Math.PI/2, 3*Math.PI/2);
                    ctx.stroke();
                }

                // 文字说明
                ctx.fillStyle = '#333';
                ctx.font = '18px Microsoft YaHei';
                ctx.textAlign = 'center';

                if (progress > 80) {
                    ctx.fillText('开区间 (3, 7) - 不包含端点 3 和 7', canvas.width / 2, 50);
                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillText('空心圆 ○ 表示不包含这个点', canvas.width / 2, 180);
                }

                progress += 2;
                if (progress <= 100) {
                    animationId = requestAnimationFrame(animate);
                }
            };

            animate();
        }

        // 显示闭区间例子
        function showClosedExample() {
            const canvas = document.getElementById('closedCanvas');
            const ctx = getCanvasContext('closedCanvas');

            drawNumberLine(ctx, canvas, 0, 10);

            const centerY = canvas.height / 2;
            const leftX = 100;
            const rightX = canvas.width - 100;

            // 绘制多个闭区间例子
            const examples = [
                { start: 0.1, end: 0.4, label: '[1, 4]', color: '#667eea' },
                { start: 0.5, end: 0.8, label: '[5, 8]', color: '#764ba2' }
            ];

            examples.forEach((example, index) => {
                const startPos = leftX + (rightX - leftX) * example.start;
                const endPos = leftX + (rightX - leftX) * example.end;

                // 区间高亮
                ctx.fillStyle = example.color + '40';
                ctx.fillRect(startPos, centerY - 15, endPos - startPos, 30);

                // 端点
                ctx.fillStyle = example.color;
                ctx.beginPath();
                ctx.arc(startPos, centerY, 6, 0, 2 * Math.PI);
                ctx.fill();
                ctx.beginPath();
                ctx.arc(endPos, centerY, 6, 0, 2 * Math.PI);
                ctx.fill();

                // 标签
                ctx.fillStyle = '#333';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(example.label, (startPos + endPos) / 2, centerY - 30 - index * 20);
            });

            ctx.fillText('闭区间包含端点！', canvas.width / 2, 50);
        }

        // 显示开区间例子
        function showOpenExample() {
            const canvas = document.getElementById('openCanvas');
            const ctx = getCanvasContext('openCanvas');

            drawNumberLine(ctx, canvas, 0, 10);

            const centerY = canvas.height / 2;
            const leftX = 100;
            const rightX = canvas.width - 100;

            // 绘制多个开区间例子
            const examples = [
                { start: 0.2, end: 0.5, label: '(2, 5)', color: '#ff6b6b' },
                { start: 0.6, end: 0.9, label: '(6, 9)', color: '#ffa726' }
            ];

            examples.forEach((example, index) => {
                const startPos = leftX + (rightX - leftX) * example.start;
                const endPos = leftX + (rightX - leftX) * example.end;

                // 区间高亮
                ctx.fillStyle = example.color + '40';
                ctx.fillRect(startPos, centerY - 15, endPos - startPos, 30);

                // 端点（空心）
                ctx.strokeStyle = example.color;
                ctx.lineWidth = 3;
                ctx.fillStyle = 'white';
                ctx.beginPath();
                ctx.arc(startPos, centerY, 6, 0, 2 * Math.PI);
                ctx.fill();
                ctx.stroke();
                ctx.beginPath();
                ctx.arc(endPos, centerY, 6, 0, 2 * Math.PI);
                ctx.fill();
                ctx.stroke();

                // 标签
                ctx.fillStyle = '#333';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(example.label, (startPos + endPos) / 2, centerY - 30 - index * 20);
            });

            ctx.fillText('开区间不包含端点！', canvas.width / 2, 50);
        }

        // 对比动画
        function animateComparison() {
            const canvas = document.getElementById('compareCanvas');
            const ctx = getCanvasContext('compareCanvas');

            let progress = 0;
            const animate = () => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制两条数轴
                const topY = canvas.height / 3;
                const bottomY = 2 * canvas.height / 3;
                const leftX = 100;
                const rightX = canvas.width - 100;

                // 上方数轴（闭区间）
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(leftX, topY);
                ctx.lineTo(rightX, topY);
                ctx.stroke();

                // 下方数轴（开区间）
                ctx.beginPath();
                ctx.moveTo(leftX, bottomY);
                ctx.lineTo(rightX, bottomY);
                ctx.stroke();

                // 绘制刻度
                for (let i = 0; i <= 10; i++) {
                    const x = leftX + (rightX - leftX) * i / 10;

                    ctx.beginPath();
                    ctx.moveTo(x, topY - 5);
                    ctx.lineTo(x, topY + 5);
                    ctx.stroke();

                    ctx.beginPath();
                    ctx.moveTo(x, bottomY - 5);
                    ctx.lineTo(x, bottomY + 5);
                    ctx.stroke();

                    ctx.fillStyle = '#333';
                    ctx.font = '12px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText(i, x, topY + 20);
                    ctx.fillText(i, x, bottomY + 20);
                }

                if (progress > 20) {
                    // 闭区间 [2, 6]
                    const startPos = leftX + (rightX - leftX) * 0.2;
                    const endPos = leftX + (rightX - leftX) * 0.6;

                    ctx.fillStyle = 'rgba(102, 126, 234, 0.3)';
                    ctx.fillRect(startPos, topY - 15, endPos - startPos, 30);

                    ctx.fillStyle = '#667eea';
                    ctx.beginPath();
                    ctx.arc(startPos, topY, 6, 0, 2 * Math.PI);
                    ctx.fill();
                    ctx.beginPath();
                    ctx.arc(endPos, topY, 6, 0, 2 * Math.PI);
                    ctx.fill();

                    ctx.fillStyle = '#333';
                    ctx.font = '16px Microsoft YaHei';
                    ctx.textAlign = 'left';
                    ctx.fillText('闭区间 [2, 6] - 包含端点', leftX, topY - 30);
                }

                if (progress > 60) {
                    // 开区间 (2, 6)
                    const startPos = leftX + (rightX - leftX) * 0.2;
                    const endPos = leftX + (rightX - leftX) * 0.6;

                    ctx.fillStyle = 'rgba(255, 107, 107, 0.3)';
                    ctx.fillRect(startPos, bottomY - 15, endPos - startPos, 30);

                    ctx.strokeStyle = '#ff6b6b';
                    ctx.lineWidth = 3;
                    ctx.fillStyle = 'white';
                    ctx.beginPath();
                    ctx.arc(startPos, bottomY, 6, 0, 2 * Math.PI);
                    ctx.fill();
                    ctx.stroke();
                    ctx.beginPath();
                    ctx.arc(endPos, bottomY, 6, 0, 2 * Math.PI);
                    ctx.fill();
                    ctx.stroke();

                    ctx.fillStyle = '#333';
                    ctx.font = '16px Microsoft YaHei';
                    ctx.textAlign = 'left';
                    ctx.fillText('开区间 (2, 6) - 不包含端点', leftX, bottomY + 40);
                }

                progress += 2;
                if (progress <= 100) {
                    animationId = requestAnimationFrame(animate);
                }
            };

            animate();
        }

        // 重置对比画布
        function resetComparison() {
            const canvas = document.getElementById('compareCanvas');
            const ctx = getCanvasContext('compareCanvas');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            if (animationId) {
                cancelAnimationFrame(animationId);
            }
        }

        // 检查答案
        function checkAnswer(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            const result = document.getElementById('quizResult');
            const nextBtn = document.getElementById('nextBtn');

            // 禁用所有选项
            options.forEach(option => {
                option.style.pointerEvents = 'none';
                if (option === element) {
                    if (isCorrect) {
                        option.classList.add('correct');
                        result.innerHTML = '🎉 正确！' + questions[currentQuestionIndex].explanation;
                        result.style.color = '#28a745';
                    } else {
                        option.classList.add('wrong');
                        result.innerHTML = '❌ 不对哦！' + questions[currentQuestionIndex].explanation;
                        result.style.color = '#dc3545';
                    }
                } else if (questions[currentQuestionIndex].options.find((opt, index) =>
                    opt.text === option.textContent && opt.correct)) {
                    option.classList.add('correct');
                }
            });

            // 显示下一题按钮
            if (currentQuestionIndex < questions.length - 1) {
                nextBtn.style.display = 'inline-block';
            } else {
                setTimeout(() => {
                    result.innerHTML += '<br><br>🎊 恭喜你完成了所有题目！你已经掌握了开区间和闭区间的概念！';
                }, 1000);
            }
        }

        // 下一题
        function nextQuestion() {
            currentQuestionIndex++;
            if (currentQuestionIndex < questions.length) {
                const question = questions[currentQuestionIndex];

                // 更新题目
                document.getElementById('quizQuestion').textContent = question.question;

                // 更新选项
                const optionsContainer = document.getElementById('quizOptions');
                optionsContainer.innerHTML = '';

                question.options.forEach((option, index) => {
                    const optionElement = document.createElement('div');
                    optionElement.className = 'quiz-option';
                    optionElement.textContent = option.text;
                    optionElement.onclick = () => checkAnswer(optionElement, option.correct);
                    optionsContainer.appendChild(optionElement);
                });

                // 清空结果和隐藏按钮
                document.getElementById('quizResult').innerHTML = '';
                document.getElementById('nextBtn').style.display = 'none';
            }
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            // 自动播放介绍动画
            setTimeout(() => {
                animateIntro();
            }, 1000);

            // 添加一些交互提示
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(btn => {
                btn.addEventListener('mouseenter', function() {
                    this.classList.add('pulse');
                });

                btn.addEventListener('mouseleave', function() {
                    this.classList.remove('pulse');
                });
            });
        });

        // 添加键盘快捷键
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case '1':
                    animateIntro();
                    break;
                case '2':
                    animateClosedInterval();
                    break;
                case '3':
                    animateOpenInterval();
                    break;
                case '4':
                    animateComparison();
                    break;
                case 'r':
                case 'R':
                    resetComparison();
                    break;
            }
        });
    </script>
</body>
</html>